# 1. First, all proxy_* directives
# 2. Then proxy_hide_header
# 3. Set backend variable
# 4. Group all add_header directives together
# 5. Conditional headers for Java routes
# 6. OPTIONS handling
# 7. Finally, the proxy_pass

# X-Content-Type-Options, 
# X-Frame-Options,
# X-XSS-Protection 
# are typical Spring Security headers.


# The header X-LEANERWAY is used for debugging purposes and
# is built by concatenating the following values:

# - server:   old|new
# - location: e.g. / or /rest or /api
# - backend:  e.g. coldfusion|java|nginx
# - optional: e.g. options


include /etc/nginx/sites-available/new-site.nginx;
