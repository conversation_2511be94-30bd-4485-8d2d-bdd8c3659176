user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
    worker_connections 768;
}

http {
    sendfile on;
    tcp_nopush on;
    types_hash_max_size 2048;
    client_max_body_size 64M;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    reset_timedout_connection on;

    gzip on;

    map "$request_method:$request_uri" $java_routes {
        include /etc/nginx/conf.d/rest_java_routes.map;
    }

    server {
        listen 127.0.0.1:80;
        listen 127.0.0.1:443 ssl;
        server_name localhost;

        ssl_certificate /etc/leanerway/certs/leanerway.crt;
        ssl_certificate_key /etc/leanerway/certs/leanerway.key;

        location /nginx_status {
            stub_status on;
            access_log off;
            auth_basic off;
            proxy_pass_request_headers off;
            allow 127.0.0.1;
            allow **********/16;
            deny all;
        }
    }

    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
