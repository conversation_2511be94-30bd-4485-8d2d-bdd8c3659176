- name: File and folder permissions
  hosts: all
  become: true

  tasks:
    - name: Add petros to www-data group
      ansible.builtin.user:
        name: petros
        groups: www-data
        append: true

    - name: Create /var/www/tms directory
      ansible.builtin.file:
        path: /var/www/leanerway
        state: directory
        owner: www-data
        group: www-data
        mode: '0775'


