#!/bin/bash

set -euo pipefail

usage() {
    echo "Usage: nginx-config <server>"
    echo "Displays NGINX configuration for the specified server"
    echo
    echo "Arguments:"
    echo "  server    Hostname or IP address of the target server"
}

if [ $# -ne 1 ]; then
    echo "Error: Exactly one argument required" >&2
    usage
    exit 1
fi

server="$1"

if ! ssh -q petros@"$server" 'sudo nginx -T'; then
    echo "Error: Failed to retrieve NGINX configuration from $server" >&2
    exit 1
fi
