- name: Uninstall Github Runner
  hosts: all
  become: true

  vars_files:
    - ../config.yaml

  vars:
    runner_org: true
    runner_user: petros
    runner_dir: /home/<USER>/runner
    runner_version: "latest"
    runner_labels: ["{{ runner_label }}"]
    runner_download_repository: "actions/runner"
    runner_state: "absent"
    reinstall_runner: false

  roles:
    - role: MonolithProjects.github_actions_runner

  tasks:
    - name: Ensure runner label is set
      ansible.builtin.assert:
        that: runner_label is defined
        fail_msg: "runner_label is not defined for this server. Ensure it is set in group_vars."

    - name: Debug Runner Labels
      ansible.builtin.debug:
        msg: "Runner labels set to {{ runner_labels }}"

    - name: Update apt cache
      ansible.builtin.apt:
        update_cache: true
        cache_valid_time: 3600
