# LeanerWay DevOps

# Target machines

1. Make sure you can connect via ssh

2. Make sure you don't need a password with ssh (execute from the manager machine) e.g.:

`ssh-copy-id petros@leanerway`

3. Allow the target user to sudo without password (or configure a sudo password in the playbooks)


To Allow a user to sudo without password in an Ubuntu Server 22.04
execute `sudo visudo` and convert the line

`%sudo ALL=(ALL:ALL) ALL` into `%sudo   ALL=(ALL:ALL) NOPASSWD: ALL`

for this to work, make sure the user we are configuring belongs to the sudo groups.
To verify execute the command `groups`:


```bash
petros@leanerway:~$ groups
petros adm cdrom sudo dip plugdev lxd
```

To add the user into the sudo group if needed:

```bash
sudo usermod -aG sudo petros
```

# Manager machine

Docs: [https://spacelift.io/blog/ansible-tutorial]

```bash
python3 -m pip install ansible
ansible --version
ansible-galaxy install MonolithProjects.github_actions_runner
```

## Create Github token 

Issue a token from  [https://github.com/settings/tokens] and configure the `config.yaml` according to the following: 

```yaml
---
github_account: *****
access_token: *****
```

# Execute playbooks:

```bash
ansible -i inventory.ini test -m ping

ansible-playbook -i inventory.ini --limit leanerway-test-server playbook_basics.yaml
ansible-playbook -i inventory.ini --limit leanerway-test-server playbook_github_install.yaml
ansible-playbook -i inventory.ini --limit leanerway-test-server playbook_nginx_validate.yaml
```

# Check that github runner is working

```bash
petros@leanerway_test:~$ cd /home/<USER>/runner
petros@leanerway_test:~/runner$ sudo ./svc.sh status
```

# Links

- [https://github.com/MonolithProjects/ansible-github_actions_runner]

# Notes:

If the github executor indicates that svc.sh does not exist, it is because the runner already exists on github for unknown reason. Force remove from Github settings and retry.
