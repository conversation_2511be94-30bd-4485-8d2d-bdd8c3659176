- name: Install SSH Keys
  hosts: all
  become: true
  
  tasks:
    - name: Ensure ~/.ssh directory exists
      become: true
      ansible.builtin.file:
        path: "~/.ssh"
        state: directory
        mode: '0700'

    - name: Copy private SSH key
      become: true
      ansible.builtin.copy:
        src: "{{ lookup('env', 'HOME') }}/.ssh/id_rsa"
        dest: "~/.ssh/id_rsa"
        mode: '0600'

    - name: Copy public SSH key
      become: true
      ansible.builtin.copy:
        src: "{{ lookup('env', 'HOME') }}/.ssh/id_rsa.pub"
        dest: "~/.ssh/id_rsa.pub"
        mode: '0644'
