#!/bin/bash
set -euo pipefail

usage() {
    echo "Usage: nginx-diff <server1> <server2>"
    echo "Shows differences between NGINX configurations of two servers"
    echo
    echo "Arguments:"
    echo "  server1    Hostname or IP of first server"
    echo "  server2    Hostname or IP of second server"
}

if [ $# -ne 2 ]; then
    echo "Error: Exactly two arguments required" >&2
    usage
    exit 1
fi

server1="$1"
server2="$2"

temp1=$(mktemp)
temp2=$(mktemp)

trap 'rm -f "$temp1" "$temp2"' EXIT

if ! ssh -q petros@"$server1" 'sudo nginx -T' > "$temp1"; then
    echo "Error: Failed to retrieve NGINX configuration from $server1" >&2
    exit 1
fi

if ! ssh -q petros@"$server2" 'sudo nginx -T' > "$temp2"; then
    echo "Error: Failed to retrieve NGINX configuration from $server2" >&2
    exit 1
fi

if command -v colordiff >/dev/null 2>&1; then
    colordiff -u "$temp1" "$temp2"
else
    diff -u "$temp1" "$temp2"
fi
