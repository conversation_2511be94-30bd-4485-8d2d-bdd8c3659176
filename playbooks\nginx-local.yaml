- name: Install NGINX in Docker
  hosts: all
  become: true

  tasks:
    - name: Update apt cache
      ansible.builtin.apt:
        update_cache: true
        cache_valid_time: 3600
      changed_when: false

    - name: Gather package facts
      ansible.builtin.package_facts:
        manager: auto

    - name: Install Nginx
      ansible.builtin.apt:
        name: nginx
        state: latest

    - name: Push NGINX Configuration [root]
      ansible.builtin.template:
        src: templates/nginx/nginx.conf
        dest: /etc/nginx/nginx.conf
        mode: '0644'
      notify:
        - Reload NGINX

    - name: Push NGINX Configuration [old site]
      ansible.builtin.template:
        src: templates/nginx/sites-available/old-site.nginx
        dest: /etc/nginx/sites-available/old-site.nginx
        mode: '0644'
      notify:
        - Reload NGINX

    - name: Push NGINX Configuration [new site]
      ansible.builtin.template:
        src: templates/nginx/sites-available/new-site.nginx
        dest: /etc/nginx/sites-available/new-site.nginx
        mode: '0644'
      notify:
        - Reload NGINX

    - name: Push NGINX Configuration [default site]
      ansible.builtin.template:
        src: templates/nginx/sites-available/default.nginx
        dest: /etc/nginx/sites-available/default
        mode: '0644'
      notify:
        - Reload NGINX

    - name: Push Java endpoints configuration
      ansible.builtin.template:
        src: templates/nginx/conf.d/rest_java_routes.map
        dest: /etc/nginx/conf.d/rest_java_routes.map
        mode: '0644'
      notify:
        - Reload NGINX

    - name: Ensure /etc/leanerway/certs directory exists
      ansible.builtin.file:
        path: /etc/leanerway/certs
        state: directory
        mode: '0755'

    - name: Copy SSL certificate leanerway.crt
      ansible.builtin.copy:
        src: certs/leanerway.crt
        dest: /etc/leanerway/certs/leanerway.crt
        mode: '0644'

    - name: Copy SSL certificate leanerway.key
      ansible.builtin.copy:
        src: certs/leanerway.key
        dest: /etc/leanerway/certs/leanerway.key
        mode: '0644'

    - name: Create /var/www/tms directory
      ansible.builtin.file:
        path: /var/www/tms
        state: directory
        owner: www-data
        group: www-data
        mode: '0755'

    - name: Create /var/www/nextjs directory
      ansible.builtin.file:
        path: /var/www/nextjs
        state: directory
        owner: www-data
        group: www-data
        mode: '0755'

    - name: Start nginx inside the container
      ansible.builtin.command: nginx
      args:
        creates: /run/nginx.pid

  handlers:
    - name: Reload NGINX
      ansible.builtin.shell: |
        nginx -t && nginx -s reload
      changed_when: false
      register: reload_result
      failed_when: reload_result.rc != 0
