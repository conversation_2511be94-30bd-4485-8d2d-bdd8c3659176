server {
    listen 80 default_server;
    listen [::]:80 default_server;

    listen 443 ssl default_server;
    listen [::]:443 ssl default_server;

    server_name *.leanerway.com;

    ssl_certificate /etc/leanerway/certs/leanerway.crt;
    ssl_certificate_key /etc/leanerway/certs/leanerway.key;

    location /rest/ {
        proxy_http_version 1.1;
        proxy_cache_bypass $http_upgrade;

        # Increase timeouts
        proxy_connect_timeout 360s;
        proxy_send_timeout 360s;
        proxy_read_timeout 360s;

        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        proxy_set_header Host $host;
        proxy_set_header Origin "";

        proxy_hide_header Upgrade;
        proxy_hide_header Connection;
        proxy_hide_header Host;
        proxy_hide_header Origin;
        proxy_hide_header Access-Control-Allow-Origin;

        set $backend "http://{{ java_target }}";
        set $x_leanerway "new|rest|java";

        if ($request_method = OPTIONS) {
            set $backend "";
            set $x_leanerway "new|rest|nginx|options";
        }

        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires 0 always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header X-LEANERWAY $x_leanerway always;

        if ($request_method = OPTIONS) {
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE, HEAD" always;
            add_header Access-Control-Allow-Headers "*" always;
            add_header Access-Control-Max-Age 1728000;
            return 204;
        }

        resolver ********** valid=300s;
        proxy_pass $backend$request_uri;
    }

    location /login/oauth2/code/ {
        proxy_http_version 1.1;
        proxy_cache_bypass $http_upgrade;

        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header Origin "";

        proxy_hide_header Upgrade;
        proxy_hide_header Connection;
        proxy_hide_header Host;
        proxy_hide_header Origin;
        proxy_hide_header Access-Control-Allow-Origin;

        add_header X-LEANERWAY "new|login|java" always;

        proxy_pass "http://{{ java_target }}";
    }

    location ^~ /api-docs/ {
        proxy_http_version 1.1;
        proxy_cache_bypass $http_upgrade;

        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header Origin "";

        proxy_hide_header Upgrade;
        proxy_hide_header Connection;
        proxy_hide_header Host;
        proxy_hide_header Origin;
        proxy_hide_header Access-Control-Allow-Origin;

        add_header X-LEANERWAY "new|api-docs|java" always;

        proxy_pass "http://{{ java_target }}";
    }

    # convert this to serve from /var/www/leanerway




    location / {
        proxy_http_version 1.1;
        proxy_cache_bypass $http_upgrade;

        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header Origin "";

        proxy_hide_header Upgrade;
        proxy_hide_header Connection;
        proxy_hide_header Host;
        proxy_hide_header Origin;
        proxy_hide_header Access-Control-Allow-Origin;

        set $x_leanerway "new|root|nextjs";

        if ($request_method = OPTIONS) {
            set $x_leanerway "new|root|nginx|options";
        }

        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires 0 always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header X-LEANERWAY $x_leanerway always;

        if ($request_method = OPTIONS) {
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE, HEAD" always;
            add_header Access-Control-Allow-Headers "*" always;
            add_header Access-Control-Max-Age 1728000;
            return 204;
        }

        proxy_pass "http://{{ new_site_target }}";
    }
}

