- name: Configure Basics on Linux Servers
  hosts: all
  become: true

  tasks:
    - name: Update apt cache
      ansible.builtin.apt:
        update_cache: true
        cache_valid_time: 3600
      changed_when: false

    - name: Install basic packages
      ansible.builtin.apt:
        name:
          - ansible
          - apt-transport-https
          - bat
          - ca-certificates
          - curl
          - duf
          - file
          - gnupg
          - gnupg-agent
          - htop
          - mc
          - mlocate
          - mysql-client
          - net-tools
          - nfs-common
          - openjdk-17-jdk
          - python3-pip
          - python3-setuptools
          - silversearcher-ag
          - software-properties-common
          - tree
          - vim
          - virtualenv
          - wget
        state: present

    - name: Add GitHub CLI signing key
      ansible.builtin.get_url:
        url: https://cli.github.com/packages/githubcli-archive-keyring.gpg
        dest: /usr/share/keyrings/githubcli-archive-keyring.gpg
        mode: '0644'
        force: true

    - name: Add GitHub CLI APT repo
      ansible.builtin.apt_repository:
        repo: "deb [arch=amd64 signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main"
        filename: github-cli
        state: present
        update_cache: true

    - name: Install latest GitHub CLI 2.x
      ansible.builtin.apt:
        name: gh
        state: latest
