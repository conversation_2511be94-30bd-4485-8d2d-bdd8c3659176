name: Deploy NGINX Configuration to Test

on:
  push:
    branches:
      - test
    paths:
      - 'playbooks/nginx.yaml'
      - 'playbooks/templates/nginx/**'
  pull_request:
    types: [closed]
    branches:
      - test
    paths:
      - 'playbooks/nginx.yaml'
      - 'playbooks/templates/nginx/**'
  workflow_dispatch:
    inputs:
      reason:
        description: 'Manual deployment'
        required: false
        default: 'Manual deployment'

jobs:
  deploy-nginx-config:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.merged == true) || github.event_name == 'workflow_dispatch'
    runs-on: [self-hosted, Linux, X64, leanerway-test-server]

    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        
      - name: Check if the branch is test
        run: |
          if [[ "${{ github.ref }}" != "refs/heads/test" ]]; then
            echo "Error: Workflow must be run on the test branch."
            exit 1
          fi

      - name: Run NGINX playbook locally
        run: |
          ansible-playbook -i "localhost," -c local -e "@group_vars/test_server.yaml" playbooks/nginx.yaml --skip-tags host
          
      - name: Validate NGINX configuration
        run: |
          ansible-playbook -i "localhost," -c local playbooks/nginx_validate.yaml
